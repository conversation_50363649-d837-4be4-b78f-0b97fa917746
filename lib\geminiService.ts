import { GoogleGenAI, Type } from "@google/genai";
import { RiasecScores, OceanScores } from './types';

// Interface untuk response dari <PERSON> (RIASEC only - backward compatibility)
export interface GeminiProfileResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
}

// Interface untuk response gabungan RIASEC + OCEAN
export interface CombinedProfileResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}

// Fungsi untuk membuat prompt yang sesuai dengan format yang diminta
function createRiasecPrompt(scores: RiasecScores): string {
  // Konversi skor dari skala 5-25 ke skala 1-100 untuk lebih mudah dipahami AI
  const convertedScores = {
    R: Math.round((scores.R / 25) * 100),
    I: Math.round((scores.I / 25) * 100),
    A: Math.round((scores.A / 25) * 100),
    S: Math.round((scores.S / 25) * 100),
    E: Math.round((scores.E / 25) * 100),
    C: Math.round((scores.C / 25) * 100)
  };

  return `Anda adalah seorang konselor karier ahli yang berspesialisasi dalam analisis kepribadian model RIASEC (Holland Codes).

Analisis skor RIASEC berikut dan hasilkan profil kepribadian dan karier yang ringkas dan terstruktur. Skor tertinggi menunjukkan minat yang paling dominan.

Data Skor RIASEC (skala 1-100):
R (Realistic): ${convertedScores.R}
I (Investigative): ${convertedScores.I}
A (Artistic): ${convertedScores.A}
S (Social): ${convertedScores.S}
E (Enterprising): ${convertedScores.E}
C (Conventional): ${convertedScores.C}

Instruksi:
1. Fokus pada 2-3 skor tertinggi untuk menentukan profil dominan
2. Berikan interpretasi yang personal dan spesifik berdasarkan kombinasi skor
3. Gunakan bahasa Indonesia yang natural dan mudah dipahami
4. Pastikan rekomendasi karier realistis dan relevan dengan konteks Indonesia
5. Kekuatan harus spesifik dan actionable, dijelaskan dengan singkat

Berikan profil yang unik dan personal berdasarkan kombinasi skor yang spesifik ini.`;
}

// Fungsi untuk membuat prompt gabungan RIASEC + OCEAN
function createCombinedPrompt(riasecScores: RiasecScores, oceanScores: OceanScores): string {
  // Konversi skor RIASEC dari skala 5-25 ke skala 1-100
  const convertedRiasec = {
    R: Math.round((riasecScores.R / 25) * 100),
    I: Math.round((riasecScores.I / 25) * 100),
    A: Math.round((riasecScores.A / 25) * 100),
    S: Math.round((riasecScores.S / 25) * 100),
    E: Math.round((riasecScores.E / 25) * 100),
    C: Math.round((riasecScores.C / 25) * 100)
  };

  // Konversi skor OCEAN dari skala 10-50 ke skala 1-100
  const convertedOcean = {
    O: Math.round((oceanScores.O / 50) * 100),
    C: Math.round((oceanScores.C / 50) * 100),
    E: Math.round((oceanScores.E / 50) * 100),
    A: Math.round((oceanScores.A / 50) * 100),
    N: Math.round((oceanScores.N / 50) * 100)
  };

  return `Anda adalah seorang konselor karier ahli yang berspesialisasi dalam analisis kepribadian menggunakan model RIASEC (Holland Codes) dan Big Five (OCEAN).

Analisis hasil tes kepribadian gabungan berikut dan hasilkan profil yang komprehensif dan terintegrasi:

DATA SKOR RIASEC (Minat Karier - skala 1-100):
R (Realistic): ${convertedRiasec.R}
I (Investigative): ${convertedRiasec.I}
A (Artistic): ${convertedRiasec.A}
S (Social): ${convertedRiasec.S}
E (Enterprising): ${convertedRiasec.E}
C (Conventional): ${convertedRiasec.C}

DATA SKOR BIG FIVE/OCEAN (Kepribadian - skala 1-100):
O (Openness): ${convertedOcean.O}
C (Conscientiousness): ${convertedOcean.C}
E (Extraversion): ${convertedOcean.E}
A (Agreeableness): ${convertedOcean.A}
N (Neuroticism): ${convertedOcean.N}

Instruksi:
1. Kombinasikan insights dari kedua model untuk memberikan analisis yang lebih akurat
2. Fokus pada bagaimana kepribadian OCEAN mendukung atau melengkapi minat RIASEC
3. Berikan rekomendasi karier yang mempertimbangkan kedua aspek
4. Identifikasi area pengembangan berdasarkan kombinasi kedua profil
5. Gunakan bahasa Indonesia yang natural dan mudah dipahami siswa SMA
6. Berikan insights kepribadian yang spesifik berdasarkan kombinasi skor
7. Pastikan career fit menjelaskan mengapa kombinasi ini cocok untuk karier tertentu

Berikan analisis yang personal dan actionable berdasarkan kombinasi unik kedua profil ini.`;
}

// Schema untuk structured output menggunakan Google GenAI
const profileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil singkat yang menarik berdasarkan 2-3 kode RIASEC tertinggi seperti (The Innovative Thinker)"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat ringkas berdasarkan profil dominan"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan profil RIASEC, dijelaskan dengan singkat"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang sesuai dengan profil"
    },
    workStyle: {
      type: Type.STRING,
      description: "Gaya kerja ideal dalam SATU kalimat"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle"]
};

// Schema untuk response gabungan RIASEC + OCEAN
const combinedProfileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil yang menggabungkan insights RIASEC dan OCEAN"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat yang mengintegrasikan kedua model"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan kombinasi RIASEC dan OCEAN"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang mempertimbangkan minat dan kepribadian"
    },
    workStyle: {
      type: Type.STRING,
      description: "Gaya kerja ideal berdasarkan kombinasi kedua profil"
    },
    developmentAreas: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "3-4 area pengembangan berdasarkan analisis gabungan"
    },
    personalityInsights: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "3-4 insights kepribadian spesifik dari kombinasi OCEAN dan RIASEC"
    },
    careerFit: {
      type: Type.STRING,
      description: "Penjelasan mengapa kombinasi profil ini cocok untuk karier yang direkomendasikan"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"]
};

// Service class untuk Gemini AI dengan structured output
export class GeminiProfileService {
  private ai: GoogleGenAI;

  constructor() {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('NEXT_PUBLIC_GEMINI_API_KEY is not set in environment variables');
    }

    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });
  }

  async generateProfile(scores: RiasecScores): Promise<GeminiProfileResponse> {
    try {
      const prompt = createRiasecPrompt(scores);

      // Menggunakan structured output dengan schema yang proper
      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: prompt,
        config: {
          responseMimeType: "application/json",
          responseSchema: profileResponseSchema,
          temperature: 0.7, // Sedikit kreativitas tapi tetap konsisten
          topP: 0.8,
          topK: 40,
        }
      });

      // Response sudah berupa JSON yang valid berkat structured output
      const responseText = response.text;
      if (!responseText) {
        throw new Error('Empty response from Gemini AI');
      }

      const profileData: GeminiProfileResponse = JSON.parse(responseText);

      // Validasi response (seharusnya tidak perlu karena schema sudah enforce structure)
      if (!profileData.profileTitle || !profileData.profileDescription ||
          !profileData.strengths || !profileData.careerSuggestions ||
          !profileData.workStyle) {
        throw new Error('Invalid response structure from Gemini AI');
      }

      // Pastikan array memiliki minimal 5 item (schema seharusnya sudah enforce ini)
      if (profileData.strengths.length < 5) {
        console.warn('Gemini returned less than 5 strengths, padding with generic ones');
        while (profileData.strengths.length < 5) {
          profileData.strengths.push('Kemampuan adaptasi yang baik');
        }
      }

      if (profileData.careerSuggestions.length < 5) {
        console.warn('Gemini returned less than 5 career suggestions, padding with generic ones');
        while (profileData.careerSuggestions.length < 5) {
          profileData.careerSuggestions.push('Konsultan profesional');
        }
      }

      return profileData;
    } catch (error) {
      console.error('Error generating profile with Gemini AI:', error);

      // Fallback ke response default jika AI gagal
      return this.getFallbackProfile(scores);
    }
  }

  // Note: cleanJsonResponse function tidak lagi diperlukan karena structured output
  // menggunakan schema yang memastikan response selalu berupa JSON yang valid

  // Fallback profile jika Gemini AI gagal
  private getFallbackProfile(scores: RiasecScores): GeminiProfileResponse {
    const maxScore = Math.max(...Object.values(scores));
    const dominantType = Object.entries(scores)
      .find(([_, score]) => score === maxScore)?.[0] || 'R';

    const fallbackProfiles: Record<string, GeminiProfileResponse> = {
      'R': {
        profileTitle: 'The Practical Implementer',
        profileDescription: 'Anda adalah seseorang yang praktis dan suka bekerja dengan tangan. Anda menikmati aktivitas fisik dan hasil yang nyata.',
        strengths: ['Keterampilan teknis', 'Pemecahan masalah praktis', 'Kemandirian', 'Ketahanan fisik', 'Orientasi hasil'],
        careerSuggestions: ['Teknisi', 'Insinyur', 'Mekanik', 'Arsitek', 'Pilot'],
        workStyle: 'Lingkungan kerja yang terstruktur dengan tugas-tugas konkret dan hasil yang terukur.'
      },
      'I': {
        profileTitle: 'The Analytical Thinker',
        profileDescription: 'Anda adalah seseorang yang analitis dan suka memecahkan masalah kompleks. Anda menikmati penelitian dan bekerja dengan ide-ide.',
        strengths: ['Kemampuan analitis', 'Pemikiran kritis', 'Penelitian', 'Pemecahan masalah', 'Objektivitas'],
        careerSuggestions: ['Peneliti', 'Ilmuwan', 'Dokter', 'Psikolog', 'Analis Data'],
        workStyle: 'Lingkungan kerja yang tenang dengan waktu untuk berpikir dan menganalisis secara mendalam.'
      },
      'A': {
        profileTitle: 'The Creative Innovator',
        profileDescription: 'Anda adalah seseorang yang kreatif dan ekspresif. Anda menikmati kegiatan seni dan menciptakan sesuatu yang baru.',
        strengths: ['Kreativitas', 'Ekspresi diri', 'Inovasi', 'Sensitivitas estetika', 'Fleksibilitas'],
        careerSuggestions: ['Desainer', 'Seniman', 'Penulis', 'Musisi', 'Fotografer'],
        workStyle: 'Lingkungan kerja yang fleksibel dengan kebebasan untuk berekspresi dan berkreasi.'
      },
      'S': {
        profileTitle: 'The People Helper',
        profileDescription: 'Anda adalah seseorang yang peduli dan suka membantu orang lain. Anda menikmati interaksi sosial dan membuat perbedaan.',
        strengths: ['Empati', 'Komunikasi', 'Kepemimpinan', 'Kerja tim', 'Motivasi orang lain'],
        careerSuggestions: ['Guru', 'Konselor', 'Perawat', 'Pekerja Sosial', 'HR Manager'],
        workStyle: 'Lingkungan kerja yang kolaboratif dengan banyak interaksi interpersonal yang bermakna.'
      },
      'E': {
        profileTitle: 'The Ambitious Leader',
        profileDescription: 'Anda adalah seseorang yang ambisius dan suka memimpin. Anda menikmati tantangan bisnis dan mempengaruhi orang lain.',
        strengths: ['Kepemimpinan', 'Persuasi', 'Pengambilan risiko', 'Orientasi hasil', 'Visi strategis'],
        careerSuggestions: ['Manajer', 'Pengusaha', 'Sales Manager', 'Konsultan', 'Direktur'],
        workStyle: 'Lingkungan kerja yang dinamis dengan peluang untuk memimpin dan mempengaruhi keputusan.'
      },
      'C': {
        profileTitle: 'The Systematic Organizer',
        profileDescription: 'Anda adalah seseorang yang terorganisir dan detail-oriented. Anda menikmati struktur dan prosedur yang jelas.',
        strengths: ['Organisasi', 'Perhatian detail', 'Keandalan', 'Efisiensi', 'Konsistensi'],
        careerSuggestions: ['Akuntan', 'Administrator', 'Auditor', 'Analis Keuangan', 'Sekretaris'],
        workStyle: 'Lingkungan kerja yang terstruktur dengan prosedur dan sistem yang jelas dan konsisten.'
      }
    };

    return fallbackProfiles[dominantType] || fallbackProfiles['R'];
  }

  // Method baru untuk analisis gabungan RIASEC + OCEAN
  async generateCombinedProfile(riasecScores: RiasecScores, oceanScores: OceanScores): Promise<CombinedProfileResponse> {
    try {
      const prompt = createCombinedPrompt(riasecScores, oceanScores);

      // Menggunakan structured output dengan schema gabungan
      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: prompt,
        config: {
          responseMimeType: "application/json",
          responseSchema: combinedProfileResponseSchema,
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
        }
      });

      const responseText = response.text;
      if (!responseText) {
        throw new Error('Empty response from Gemini AI');
      }

      const profileData: CombinedProfileResponse = JSON.parse(responseText);

      // Validasi response
      if (!profileData.profileTitle || !profileData.profileDescription ||
          !profileData.strengths || !profileData.careerSuggestions ||
          !profileData.workStyle || !profileData.developmentAreas ||
          !profileData.personalityInsights || !profileData.careerFit) {
        throw new Error('Invalid response structure from Gemini AI');
      }

      return profileData;
    } catch (error) {
      console.error('Error generating combined profile with Gemini AI:', error);

      // Fallback ke response default jika AI gagal
      return this.getCombinedFallbackProfile(riasecScores, oceanScores);
    }
  }

  // Fallback profile untuk analisis gabungan
  private getCombinedFallbackProfile(riasecScores: RiasecScores, oceanScores: OceanScores): CombinedProfileResponse {
    const maxRiasecScore = Math.max(...Object.values(riasecScores));
    const dominantRiasecType = Object.entries(riasecScores)
      .find(([_, score]) => score === maxRiasecScore)?.[0] || 'R';

    return {
      profileTitle: 'Profil Kepribadian Terintegrasi',
      profileDescription: 'Anda memiliki kombinasi unik antara minat karier dan kepribadian yang dapat dikembangkan lebih lanjut. Profil ini menggabungkan aspek minat dan karakteristik kepribadian Anda.',
      strengths: [
        'Kemampuan adaptasi yang baik',
        'Potensi pengembangan yang beragam',
        'Fleksibilitas dalam pendekatan',
        'Keseimbangan antara minat dan kepribadian',
        'Kapasitas untuk pertumbuhan personal'
      ],
      careerSuggestions: [
        'Konsultan multidisiplin',
        'Koordinator proyek',
        'Spesialis pengembangan',
        'Analis sistem',
        'Manajer operasional'
      ],
      workStyle: 'Lingkungan kerja yang seimbang dengan variasi tugas dan interaksi yang disesuaikan dengan preferensi personal.',
      developmentAreas: [
        'Pengembangan keterampilan komunikasi',
        'Peningkatan kemampuan analitis',
        'Penguatan kepercayaan diri',
        'Pengembangan keterampilan teknis'
      ],
      personalityInsights: [
        'Memiliki potensi untuk berkembang di berbagai bidang',
        'Dapat menyesuaikan diri dengan berbagai situasi kerja',
        'Membutuhkan lingkungan yang mendukung pertumbuhan',
        'Cocok untuk peran yang memerlukan fleksibilitas'
      ],
      careerFit: 'Kombinasi profil Anda menunjukkan fleksibilitas dan adaptabilitas yang tinggi, cocok untuk karier yang memerlukan keseimbangan antara berbagai keterampilan dan kemampuan interpersonal.'
    };
  }
}

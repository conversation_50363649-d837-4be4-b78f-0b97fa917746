'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { RiasecScores, OceanScores, Answer, OceanAnswer } from '@/lib/types';

// Interface untuk state profil lengkap
export interface ProfileState {
  // RIASEC data
  riasecAnswers: Record<number, number>;
  riasecScores: RiasecScores | null;
  riasecCompleted: boolean;
  
  // OCEAN data
  oceanAnswers: Record<number, number>;
  oceanScores: OceanScores | null;
  oceanCompleted: boolean;
  
  // Combined profile data
  profileGenerated: boolean;
  profileData: CombinedProfileData | null;
}

// Interface untuk data profil gabungan
export interface CombinedProfileData {
  riasecProfile: {
    scores: RiasecScores;
    dominantTypes: string[];
    interpretation: string;
  };
  oceanProfile: {
    scores: OceanScores;
    traits: OceanTraitLevel[];
    interpretation: string;
  };
  combinedAnalysis: {
    profileTitle: string;
    profileDescription: string;
    strengths: string[];
    careerSuggestions: string[];
    workStyle: string;
    developmentAreas: string[];
  };
}

// Interface untuk level trait OCEAN
export interface OceanTraitLevel {
  trait: string;
  level: 'Rendah' | 'Sedang' | 'Tinggi';
  score: number;
  description: string;
}

// Action types untuk reducer
type ProfileAction =
  | { type: 'SET_RIASEC_ANSWER'; questionId: number; score: number }
  | { type: 'SET_RIASEC_SCORES'; scores: RiasecScores }
  | { type: 'SET_RIASEC_COMPLETED'; completed: boolean }
  | { type: 'SET_OCEAN_ANSWER'; questionId: number; score: number }
  | { type: 'SET_OCEAN_SCORES'; scores: OceanScores }
  | { type: 'SET_OCEAN_COMPLETED'; completed: boolean }
  | { type: 'SET_PROFILE_DATA'; data: CombinedProfileData }
  | { type: 'RESET_PROFILE' };

// Initial state
const initialState: ProfileState = {
  riasecAnswers: {},
  riasecScores: null,
  riasecCompleted: false,
  oceanAnswers: {},
  oceanScores: null,
  oceanCompleted: false,
  profileGenerated: false,
  profileData: null,
};

// Reducer function
function profileReducer(state: ProfileState, action: ProfileAction): ProfileState {
  switch (action.type) {
    case 'SET_RIASEC_ANSWER':
      return {
        ...state,
        riasecAnswers: {
          ...state.riasecAnswers,
          [action.questionId]: action.score,
        },
      };
    
    case 'SET_RIASEC_SCORES':
      return {
        ...state,
        riasecScores: action.scores,
      };
    
    case 'SET_RIASEC_COMPLETED':
      return {
        ...state,
        riasecCompleted: action.completed,
      };
    
    case 'SET_OCEAN_ANSWER':
      return {
        ...state,
        oceanAnswers: {
          ...state.oceanAnswers,
          [action.questionId]: action.score,
        },
      };
    
    case 'SET_OCEAN_SCORES':
      return {
        ...state,
        oceanScores: action.scores,
      };
    
    case 'SET_OCEAN_COMPLETED':
      return {
        ...state,
        oceanCompleted: action.completed,
      };
    
    case 'SET_PROFILE_DATA':
      return {
        ...state,
        profileData: action.data,
        profileGenerated: true,
      };
    
    case 'RESET_PROFILE':
      return initialState;
    
    default:
      return state;
  }
}

// Context interface
interface ProfileContextType {
  state: ProfileState;
  dispatch: React.Dispatch<ProfileAction>;
  
  // Helper functions
  setRiasecAnswer: (questionId: number, score: number) => void;
  setRiasecScores: (scores: RiasecScores) => void;
  setRiasecCompleted: (completed: boolean) => void;
  
  setOceanAnswer: (questionId: number, score: number) => void;
  setOceanScores: (scores: OceanScores) => void;
  setOceanCompleted: (completed: boolean) => void;
  
  setProfileData: (data: CombinedProfileData) => void;
  resetProfile: () => void;
  
  // Computed properties
  isRiasecComplete: boolean;
  isOceanComplete: boolean;
  canGenerateProfile: boolean;
}

// Create context
const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

// Provider component
export function ProfileProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(profileReducer, initialState);

  // Helper functions
  const setRiasecAnswer = (questionId: number, score: number) => {
    dispatch({ type: 'SET_RIASEC_ANSWER', questionId, score });
  };

  const setRiasecScores = (scores: RiasecScores) => {
    dispatch({ type: 'SET_RIASEC_SCORES', scores });
  };

  const setRiasecCompleted = (completed: boolean) => {
    dispatch({ type: 'SET_RIASEC_COMPLETED', completed });
  };

  const setOceanAnswer = (questionId: number, score: number) => {
    dispatch({ type: 'SET_OCEAN_ANSWER', questionId, score });
  };

  const setOceanScores = (scores: OceanScores) => {
    dispatch({ type: 'SET_OCEAN_SCORES', scores });
  };

  const setOceanCompleted = (completed: boolean) => {
    dispatch({ type: 'SET_OCEAN_COMPLETED', completed });
  };

  const setProfileData = (data: CombinedProfileData) => {
    dispatch({ type: 'SET_PROFILE_DATA', data });
  };

  const resetProfile = () => {
    dispatch({ type: 'RESET_PROFILE' });
  };

  // Computed properties
  const isRiasecComplete = Object.keys(state.riasecAnswers).length >= 30; // Assuming 30 RIASEC questions
  const isOceanComplete = Object.keys(state.oceanAnswers).length >= 50; // 50 OCEAN questions
  const canGenerateProfile = state.riasecCompleted && state.oceanCompleted;

  const contextValue: ProfileContextType = {
    state,
    dispatch,
    setRiasecAnswer,
    setRiasecScores,
    setRiasecCompleted,
    setOceanAnswer,
    setOceanScores,
    setOceanCompleted,
    setProfileData,
    resetProfile,
    isRiasecComplete,
    isOceanComplete,
    canGenerateProfile,
  };

  return (
    <ProfileContext.Provider value={contextValue}>
      {children}
    </ProfileContext.Provider>
  );
}

// Hook untuk menggunakan context
export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}

// Utility functions untuk menghitung level trait OCEAN
export function getOceanTraitLevel(score: number): 'Rendah' | 'Sedang' | 'Tinggi' {
  // Skor OCEAN berkisar 10-50 (10 pertanyaan x 1-5 skala)
  if (score <= 25) return 'Rendah';
  if (score <= 35) return 'Sedang';
  return 'Tinggi';
}

export function getOceanTraitDescription(trait: string, level: 'Rendah' | 'Sedang' | 'Tinggi'): string {
  const descriptions = {
    'Openness': {
      'Rendah': 'Cenderung praktis dan konvensional dalam pendekatan',
      'Sedang': 'Seimbang antara praktis dan terbuka terhadap ide baru',
      'Tinggi': 'Sangat kreatif, imajinatif, dan terbuka terhadap pengalaman baru'
    },
    'Conscientiousness': {
      'Rendah': 'Cenderung fleksibel dan spontan dalam pendekatan',
      'Sedang': 'Seimbang antara terorganisir dan fleksibel',
      'Tinggi': 'Sangat terorganisir, disiplin, dan berorientasi pada tujuan'
    },
    'Extraversion': {
      'Rendah': 'Cenderung introspektif dan menyukai ketenangan',
      'Sedang': 'Seimbang antara sosial dan waktu pribadi',
      'Tinggi': 'Sangat sosial, energik, dan mencari stimulasi eksternal'
    },
    'Agreeableness': {
      'Rendah': 'Cenderung kompetitif dan skeptis',
      'Sedang': 'Seimbang antara kooperatif dan asertif',
      'Tinggi': 'Sangat kooperatif, empati, dan mudah percaya'
    },
    'Neuroticism': {
      'Rendah': 'Sangat stabil secara emosional dan tenang',
      'Sedang': 'Cukup stabil dengan sesekali mengalami stres',
      'Tinggi': 'Sensitif terhadap stres dan emosi negatif'
    }
  };

  return descriptions[trait as keyof typeof descriptions]?.[level] || 'Deskripsi tidak tersedia';
}

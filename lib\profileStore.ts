import { RiasecType, RiasecScores, OceanScores, OceanType } from './types';
import { GeminiProfileService, GeminiProfileResponse, CombinedProfileResponse } from './geminiService';

// Interface untuk profil interpretasi RIASEC saja (backward compatibility)
export interface ProfileInterpretation {
  dominantTypes: RiasecType[];
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  developmentAreas: string[];
  workEnvironment: string;
}

// Interface untuk profil interpretasi gabungan RIASEC + OCEAN
export interface CombinedProfileInterpretation {
  // RIASEC data
  riasecData: {
    scores: RiasecScores;
    dominantTypes: RiasecType[];
    level: string;
  };

  // OCEAN data
  oceanData: {
    scores: OceanScores;
    traits: OceanTraitSummary[];
    personalityType: string;
  };

  // Combined analysis
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}

// Interface untuk ringkasan trait OCEAN
export interface OceanTraitSummary {
  trait: OceanType;
  name: string;
  score: number;
  level: 'Rendah' | 'Sedang' | 'Tinggi';
  description: string;
}

// Fungsi untuk mendapatkan interpretasi profil berdasarkan skor menggunakan Gemini AI
export async function getProfileInterpretation(scores: RiasecScores): Promise<ProfileInterpretation> {
  try {
    // Inisialisasi Gemini service
    const geminiService = new GeminiProfileService();

    // Generate profile menggunakan AI
    const aiProfile: GeminiProfileResponse = await geminiService.generateProfile(scores);

    // Hitung dominant types untuk development areas
    const sortedScores = Object.entries(scores)
      .sort(([,a], [,b]) => b - a)
      .map(([type, score]) => ({ type: type as RiasecType, score }));

    const maxScore = sortedScores[0].score;
    const dominantTypes = sortedScores
      .filter(item => item.score === maxScore)
      .map(item => item.type);

    // Generate development areas berdasarkan skor terendah (hanya jika skor < 10)
    const lowScoreTypes = sortedScores
      .filter(item => item.score < 10)
      .map(item => item.type);

    const developmentAreas = lowScoreTypes.map(type => {
      switch(type) {
        case 'R': return 'Keterampilan praktis dan teknis';
        case 'I': return 'Kemampuan analitis dan penelitian';
        case 'A': return 'Kreativitas dan ekspresi artistik';
        case 'S': return 'Keterampilan interpersonal dan empati';
        case 'E': return 'Kepemimpinan dan kemampuan persuasi';
        case 'C': return 'Organisasi dan perhatian terhadap detail';
        default: return 'Area pengembangan umum';
      }
    });

    return {
      dominantTypes,
      profileTitle: aiProfile.profileTitle,
      profileDescription: aiProfile.profileDescription,
      strengths: aiProfile.strengths,
      careerSuggestions: aiProfile.careerSuggestions,
      developmentAreas,
      workEnvironment: aiProfile.workStyle
    };
  } catch (error) {
    console.error('Error getting AI profile interpretation:', error);

    // Fallback ke metode lama jika AI gagal
    return getFallbackProfileInterpretation(scores);
  }
}

// Fungsi fallback sederhana jika Gemini AI gagal
function getFallbackProfileInterpretation(scores: RiasecScores): ProfileInterpretation {
  // Urutkan skor dari tertinggi ke terendah
  const sortedScores = Object.entries(scores)
    .sort(([,a], [,b]) => b - a)
    .map(([type, score]) => ({ type: type as RiasecType, score }));

  // Ambil skor tertinggi
  const maxScore = sortedScores[0].score;
  const dominantTypes = sortedScores
    .filter(item => item.score === maxScore)
    .map(item => item.type);

  // Generate development areas berdasarkan skor terendah (hanya jika skor < 10)
  const lowScoreTypes = sortedScores
    .filter(item => item.score < 10)
    .map(item => item.type);

  const developmentAreas = lowScoreTypes.map(type => {
    switch(type) {
      case 'R': return 'Keterampilan praktis dan teknis';
      case 'I': return 'Kemampuan analitis dan penelitian';
      case 'A': return 'Kreativitas dan ekspresi artistik';
      case 'S': return 'Keterampilan interpersonal dan empati';
      case 'E': return 'Kepemimpinan dan kemampuan persuasi';
      case 'C': return 'Organisasi dan perhatian terhadap detail';
      default: return 'Area pengembangan umum';
    }
  });

  // Fallback profile sederhana berdasarkan tipe dominan
  const primaryType = dominantTypes[0];
  const fallbackProfiles: Record<RiasecType, Omit<ProfileInterpretation, 'dominantTypes' | 'developmentAreas'>> = {
    'R': {
      profileTitle: 'Profil Praktis (Realistic)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas praktis dan hands-on. Anda cenderung menyukai pekerjaan yang melibatkan alat, mesin, atau aktivitas fisik.',
      strengths: ['Keterampilan teknis', 'Pemecahan masalah praktis', 'Kemandirian', 'Ketahanan fisik', 'Orientasi hasil'],
      careerSuggestions: ['Teknisi', 'Insinyur', 'Mekanik', 'Arsitek', 'Pilot'],
      workEnvironment: 'Lingkungan kerja yang terstruktur dengan tugas-tugas konkret dan hasil yang terukur.'
    },
    'I': {
      profileTitle: 'Profil Analitis (Investigative)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas analitis dan penelitian. Anda cenderung menyukai pemecahan masalah kompleks dan bekerja dengan ide-ide.',
      strengths: ['Kemampuan analitis', 'Pemikiran kritis', 'Penelitian', 'Pemecahan masalah', 'Objektivitas'],
      careerSuggestions: ['Peneliti', 'Ilmuwan', 'Dokter', 'Psikolog', 'Analis Data'],
      workEnvironment: 'Lingkungan kerja yang tenang dengan waktu untuk berpikir dan menganalisis secara mendalam.'
    },
    'A': {
      profileTitle: 'Profil Kreatif (Artistic)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas kreatif dan artistik. Anda cenderung menyukai ekspresi diri dan menciptakan sesuatu yang baru.',
      strengths: ['Kreativitas', 'Ekspresi diri', 'Inovasi', 'Sensitivitas estetika', 'Fleksibilitas'],
      careerSuggestions: ['Desainer', 'Seniman', 'Penulis', 'Musisi', 'Fotografer'],
      workEnvironment: 'Lingkungan kerja yang fleksibel dengan kebebasan untuk berekspresi dan berkreasi.'
    },
    'S': {
      profileTitle: 'Profil Sosial (Social)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas sosial dan membantu orang lain. Anda cenderung menyukai interaksi interpersonal dan membuat dampak positif.',
      strengths: ['Empati', 'Komunikasi', 'Kepemimpinan', 'Kerja tim', 'Motivasi orang lain'],
      careerSuggestions: ['Guru', 'Konselor', 'Perawat', 'Pekerja Sosial', 'HR Manager'],
      workEnvironment: 'Lingkungan kerja yang kolaboratif dengan banyak interaksi interpersonal yang bermakna.'
    },
    'E': {
      profileTitle: 'Profil Enterprising (Enterprising)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas kepemimpinan dan bisnis. Anda cenderung menyukai tantangan, persuasi, dan mempengaruhi orang lain.',
      strengths: ['Kepemimpinan', 'Persuasi', 'Pengambilan risiko', 'Orientasi hasil', 'Visi strategis'],
      careerSuggestions: ['Manajer', 'Pengusaha', 'Sales Manager', 'Konsultan', 'Direktur'],
      workEnvironment: 'Lingkungan kerja yang dinamis dengan peluang untuk memimpin dan mempengaruhi keputusan.'
    },
    'C': {
      profileTitle: 'Profil Konvensional (Conventional)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas yang terorganisir dan sistematis. Anda cenderung menyukai struktur, detail, dan prosedur yang jelas.',
      strengths: ['Organisasi', 'Perhatian detail', 'Keandalan', 'Efisiensi', 'Konsistensi'],
      careerSuggestions: ['Akuntan', 'Administrator', 'Auditor', 'Analis Keuangan', 'Sekretaris'],
      workEnvironment: 'Lingkungan kerja yang terstruktur dengan prosedur dan sistem yang jelas dan konsisten.'
    }
  };

  const fallbackProfile = fallbackProfiles[primaryType] || fallbackProfiles['R'];

  return {
    dominantTypes,
    profileTitle: fallbackProfile.profileTitle,
    profileDescription: fallbackProfile.profileDescription,
    strengths: fallbackProfile.strengths,
    careerSuggestions: fallbackProfile.careerSuggestions,
    developmentAreas,
    workEnvironment: fallbackProfile.workEnvironment
  };
}

// Fungsi untuk mendapatkan level interpretasi skor
export function getScoreLevel(score: number): { level: string; color: string; description: string } {
  if (score >= 20) {
    return {
      level: 'Sangat Tinggi',
      color: 'text-green-600 bg-green-50 border-green-200',
      description: 'Minat yang sangat kuat pada area ini'
    };
  } else if (score >= 15) {
    return {
      level: 'Tinggi',
      color: 'text-blue-600 bg-blue-50 border-blue-200',
      description: 'Minat yang cukup kuat pada area ini'
    };
  } else if (score >= 10) {
    return {
      level: 'Sedang',
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      description: 'Minat yang moderat pada area ini'
    };
  } else {
    return {
      level: 'Rendah',
      color: 'text-gray-600 bg-gray-50 border-gray-200',
      description: 'Minat yang rendah pada area ini'
    };
  }
}

// Fungsi untuk mendapatkan interpretasi profil gabungan RIASEC + OCEAN
export async function getCombinedProfileInterpretation(
  riasecScores: RiasecScores,
  oceanScores: OceanScores
): Promise<CombinedProfileInterpretation> {
  try {
    // Inisialisasi Gemini service
    const geminiService = new GeminiProfileService();

    // Generate combined profile menggunakan AI
    const aiProfile: CombinedProfileResponse = await geminiService.generateCombinedProfile(riasecScores, oceanScores);

    // Hitung dominant RIASEC types
    const sortedRiasecScores = Object.entries(riasecScores)
      .sort(([,a], [,b]) => b - a)
      .map(([type, score]) => ({ type: type as RiasecType, score }));

    const maxRiasecScore = sortedRiasecScores[0].score;
    const dominantRiasecTypes = sortedRiasecScores
      .filter(item => item.score === maxRiasecScore)
      .map(item => item.type);

    // Generate OCEAN trait summaries
    const oceanTraits: OceanTraitSummary[] = [
      {
        trait: 'O',
        name: 'Openness',
        score: oceanScores.O,
        level: getOceanTraitLevel(oceanScores.O),
        description: getOceanTraitDescription('O', getOceanTraitLevel(oceanScores.O))
      },
      {
        trait: 'C',
        name: 'Conscientiousness',
        score: oceanScores.C,
        level: getOceanTraitLevel(oceanScores.C),
        description: getOceanTraitDescription('C', getOceanTraitLevel(oceanScores.C))
      },
      {
        trait: 'E',
        name: 'Extraversion',
        score: oceanScores.E,
        level: getOceanTraitLevel(oceanScores.E),
        description: getOceanTraitDescription('E', getOceanTraitLevel(oceanScores.E))
      },
      {
        trait: 'A',
        name: 'Agreeableness',
        score: oceanScores.A,
        level: getOceanTraitLevel(oceanScores.A),
        description: getOceanTraitDescription('A', getOceanTraitLevel(oceanScores.A))
      },
      {
        trait: 'N',
        name: 'Neuroticism',
        score: oceanScores.N,
        level: getOceanTraitLevel(oceanScores.N),
        description: getOceanTraitDescription('N', getOceanTraitLevel(oceanScores.N))
      }
    ];

    // Determine personality type based on highest OCEAN scores
    const sortedOceanTraits = oceanTraits
      .sort((a, b) => b.score - a.score)
      .slice(0, 2);

    const personalityType = `${sortedOceanTraits[0].name} - ${sortedOceanTraits[1].name}`;

    return {
      riasecData: {
        scores: riasecScores,
        dominantTypes: dominantRiasecTypes,
        level: getRiasecLevelDescription(maxRiasecScore)
      },
      oceanData: {
        scores: oceanScores,
        traits: oceanTraits,
        personalityType
      },
      profileTitle: aiProfile.profileTitle,
      profileDescription: aiProfile.profileDescription,
      strengths: aiProfile.strengths,
      careerSuggestions: aiProfile.careerSuggestions,
      workStyle: aiProfile.workStyle,
      developmentAreas: aiProfile.developmentAreas,
      personalityInsights: aiProfile.personalityInsights,
      careerFit: aiProfile.careerFit
    };
  } catch (error) {
    console.error('Error getting combined AI profile interpretation:', error);

    // Fallback ke response default jika AI gagal
    return getCombinedFallbackProfile(riasecScores, oceanScores);
  }
}

// Helper functions untuk OCEAN trait analysis
function getOceanTraitLevel(score: number): 'Rendah' | 'Sedang' | 'Tinggi' {
  // Skor OCEAN berkisar 10-50 (10 pertanyaan x 1-5 skala)
  if (score <= 25) return 'Rendah';
  if (score <= 35) return 'Sedang';
  return 'Tinggi';
}

function getOceanTraitDescription(trait: string, level: 'Rendah' | 'Sedang' | 'Tinggi'): string {
  const descriptions = {
    'O': {
      'Rendah': 'Cenderung praktis dan konvensional',
      'Sedang': 'Seimbang antara praktis dan kreatif',
      'Tinggi': 'Sangat kreatif dan terbuka terhadap ide baru'
    },
    'C': {
      'Rendah': 'Cenderung fleksibel dan spontan',
      'Sedang': 'Seimbang antara terorganisir dan fleksibel',
      'Tinggi': 'Sangat terorganisir dan disiplin'
    },
    'E': {
      'Rendah': 'Cenderung introspektif dan tenang',
      'Sedang': 'Seimbang antara sosial dan pribadi',
      'Tinggi': 'Sangat sosial dan energik'
    },
    'A': {
      'Rendah': 'Cenderung kompetitif dan skeptis',
      'Sedang': 'Seimbang antara kooperatif dan asertif',
      'Tinggi': 'Sangat kooperatif dan empati'
    },
    'N': {
      'Rendah': 'Sangat stabil secara emosional',
      'Sedang': 'Cukup stabil dengan sesekali stres',
      'Tinggi': 'Sensitif terhadap stres dan emosi'
    }
  };

  return descriptions[trait as keyof typeof descriptions]?.[level] || 'Deskripsi tidak tersedia';
}

function getRiasecLevelDescription(score: number): string {
  if (score >= 20) return 'Sangat Tinggi';
  if (score >= 15) return 'Tinggi';
  if (score >= 10) return 'Sedang';
  return 'Rendah';
}

// Fallback profile untuk analisis gabungan
function getCombinedFallbackProfile(riasecScores: RiasecScores, oceanScores: OceanScores): CombinedProfileInterpretation {
  const maxRiasecScore = Math.max(...Object.values(riasecScores));
  const dominantRiasecType = Object.entries(riasecScores)
    .find(([_, score]) => score === maxRiasecScore)?.[0] as RiasecType || 'R';

  const oceanTraits: OceanTraitSummary[] = [
    {
      trait: 'O',
      name: 'Openness',
      score: oceanScores.O,
      level: getOceanTraitLevel(oceanScores.O),
      description: getOceanTraitDescription('O', getOceanTraitLevel(oceanScores.O))
    },
    {
      trait: 'C',
      name: 'Conscientiousness',
      score: oceanScores.C,
      level: getOceanTraitLevel(oceanScores.C),
      description: getOceanTraitDescription('C', getOceanTraitLevel(oceanScores.C))
    },
    {
      trait: 'E',
      name: 'Extraversion',
      score: oceanScores.E,
      level: getOceanTraitLevel(oceanScores.E),
      description: getOceanTraitDescription('E', getOceanTraitLevel(oceanScores.E))
    },
    {
      trait: 'A',
      name: 'Agreeableness',
      score: oceanScores.A,
      level: getOceanTraitLevel(oceanScores.A),
      description: getOceanTraitDescription('A', getOceanTraitLevel(oceanScores.A))
    },
    {
      trait: 'N',
      name: 'Neuroticism',
      score: oceanScores.N,
      level: getOceanTraitLevel(oceanScores.N),
      description: getOceanTraitDescription('N', getOceanTraitLevel(oceanScores.N))
    }
  ];

  return {
    riasecData: {
      scores: riasecScores,
      dominantTypes: [dominantRiasecType],
      level: getRiasecLevelDescription(maxRiasecScore)
    },
    oceanData: {
      scores: oceanScores,
      traits: oceanTraits,
      personalityType: 'Profil Kepribadian Seimbang'
    },
    profileTitle: 'Profil Kepribadian dan Minat Terintegrasi',
    profileDescription: 'Anda memiliki kombinasi unik antara minat karier dan kepribadian yang dapat dikembangkan lebih lanjut untuk mencapai potensi maksimal.',
    strengths: [
      'Kemampuan adaptasi yang baik',
      'Potensi pengembangan yang beragam',
      'Fleksibilitas dalam pendekatan',
      'Keseimbangan antara minat dan kepribadian',
      'Kapasitas untuk pertumbuhan personal'
    ],
    careerSuggestions: [
      'Konsultan multidisiplin',
      'Koordinator proyek',
      'Spesialis pengembangan',
      'Analis sistem',
      'Manajer operasional'
    ],
    workStyle: 'Lingkungan kerja yang seimbang dengan variasi tugas dan interaksi yang disesuaikan dengan preferensi personal.',
    developmentAreas: [
      'Pengembangan keterampilan komunikasi',
      'Peningkatan kemampuan analitis',
      'Penguatan kepercayaan diri',
      'Pengembangan keterampilan teknis'
    ],
    personalityInsights: [
      'Memiliki potensi untuk berkembang di berbagai bidang',
      'Dapat menyesuaikan diri dengan berbagai situasi kerja',
      'Membutuhkan lingkungan yang mendukung pertumbuhan',
      'Cocok untuk peran yang memerlukan fleksibilitas'
    ],
    careerFit: 'Kombinasi profil Anda menunjukkan fleksibilitas dan adaptabilitas yang tinggi, cocok untuk karier yang memerlukan keseimbangan antara berbagai keterampilan dan kemampuan interpersonal.'
  };
}

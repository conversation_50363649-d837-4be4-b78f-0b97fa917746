// RIASEC Types
export type RiasecType = 'R' | 'I' | 'A' | 'S' | 'E' | 'C';

// OCEAN Types (Big Five Personality)
export type OceanType = 'O' | 'C' | 'E' | 'A' | 'N';

// Question interface for RIASEC
export interface Question {
  id: number;
  text: string;
  riasec_type: RiasecType;
}

// Question interface for OCEAN
export interface OceanQuestion {
  id: number;
  text: string;
  ocean_type: OceanType;
  isReversed: boolean; // For reverse-scored items
}

// Answer interface for storing user responses
export interface Answer {
  questionId: number;
  score: number; // 1-5 Likert scale
}

// OCEAN Answer interface for storing user responses
export interface OceanAnswer {
  questionId: number;
  score: number; // 1-5 Likert scale
}

// RIASEC scores interface
export interface RiasecScores {
  R: number; // Realistic
  I: number; // Investigative
  A: number; // Artistic
  S: number; // Social
  E: number; // Enterprising
  C: number; // Conventional
}

// OCEAN scores interface
export interface OceanScores {
  O: number; // Openness to Experience
  C: number; // Conscientiousness
  E: number; // Extraversion
  A: number; // Agreeableness
  N: number; // Neuroticism
}

// RIASEC descriptions
export interface RiasecDescription {
  type: RiasecType;
  name: string;
  description: string;
}

// OCEAN descriptions
export interface OceanDescription {
  type: OceanType;
  name: string;
  description: string;
}

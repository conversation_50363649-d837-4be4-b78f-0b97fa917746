import { OceanQuestion, OceanDescription } from './types';

// Big Five (OCEAN) Personality Questions (50 total - 10 per dimension)
export const oceanQuestions: OceanQuestion[] = [
  // Openness to Experience (O) - 10 questions
  {
    id: 1,
    text: "Saya suka mencoba hal-hal baru dan berbeda.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 2,
    text: "Saya memiliki imajinasi yang aktif.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 3,
    text: "Saya tertarik pada ide-ide abstrak dan filosofis.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 4,
    text: "Saya lebih suka rutinitas yang sudah familiar daripada mencoba sesuatu yang baru.",
    ocean_type: 'O',
    isReversed: true
  },
  {
    id: 5,
    text: "Saya menikmati seni, musik, dan sastra.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 6,
    text: "Saya suka berpikir tentang kemungkinan-kemungkinan yang belum terjadi.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 7,
    text: "Saya cenderung konservatif dalam cara berpikir.",
    ocean_type: 'O',
    isReversed: true
  },
  {
    id: 8,
    text: "Saya tertarik untuk mempelajari budaya dan tradisi yang berbeda.",
    ocean_type: 'O',
    isReversed: false
  },
  {
    id: 9,
    text: "Saya lebih suka hal-hal yang praktis daripada yang teoritis.",
    ocean_type: 'O',
    isReversed: true
  },
  {
    id: 10,
    text: "Saya sering merenungkan makna hidup dan eksistensi.",
    ocean_type: 'O',
    isReversed: false
  },

  // Conscientiousness (C) - 10 questions
  {
    id: 11,
    text: "Saya selalu menyelesaikan tugas tepat waktu.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 12,
    text: "Saya adalah orang yang terorganisir dan rapi.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 13,
    text: "Saya sering menunda-nunda pekerjaan.",
    ocean_type: 'C',
    isReversed: true
  },
  {
    id: 14,
    text: "Saya memperhatikan detail dalam pekerjaan saya.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 15,
    text: "Saya mudah teralihkan dari tugas yang sedang dikerjakan.",
    ocean_type: 'C',
    isReversed: true
  },
  {
    id: 16,
    text: "Saya membuat rencana dan mengikutinya dengan konsisten.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 17,
    text: "Ruang kerja saya sering berantakan.",
    ocean_type: 'C',
    isReversed: true
  },
  {
    id: 18,
    text: "Saya dapat diandalkan untuk menepati janji.",
    ocean_type: 'C',
    isReversed: false
  },
  {
    id: 19,
    text: "Saya cenderung ceroboh dalam melakukan sesuatu.",
    ocean_type: 'C',
    isReversed: true
  },
  {
    id: 20,
    text: "Saya berusaha keras untuk mencapai tujuan saya.",
    ocean_type: 'C',
    isReversed: false
  },

  // Extraversion (E) - 10 questions
  {
    id: 21,
    text: "Saya merasa berenergi ketika berada di sekitar orang lain.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 22,
    text: "Saya mudah memulai percakapan dengan orang yang baru dikenal.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 23,
    text: "Saya lebih suka menghabiskan waktu sendirian daripada dengan orang lain.",
    ocean_type: 'E',
    isReversed: true
  },
  {
    id: 24,
    text: "Saya suka menjadi pusat perhatian.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 25,
    text: "Saya merasa tidak nyaman dalam situasi sosial yang besar.",
    ocean_type: 'E',
    isReversed: true
  },
  {
    id: 26,
    text: "Saya aktif dan penuh semangat dalam kegiatan kelompok.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 27,
    text: "Saya cenderung pendiam dalam pertemuan atau diskusi.",
    ocean_type: 'E',
    isReversed: true
  },
  {
    id: 28,
    text: "Saya suka bertemu dengan orang-orang baru.",
    ocean_type: 'E',
    isReversed: false
  },
  {
    id: 29,
    text: "Saya merasa lelah setelah menghabiskan waktu lama dengan banyak orang.",
    ocean_type: 'E',
    isReversed: true
  },
  {
    id: 30,
    text: "Saya mudah mengekspresikan perasaan dan emosi saya.",
    ocean_type: 'E',
    isReversed: false
  },

  // Agreeableness (A) - 10 questions
  {
    id: 31,
    text: "Saya selalu berusaha membantu orang lain.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 32,
    text: "Saya mudah mempercayai orang lain.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 33,
    text: "Saya cenderung skeptis terhadap motif orang lain.",
    ocean_type: 'A',
    isReversed: true
  },
  {
    id: 34,
    text: "Saya merasa empati terhadap penderitaan orang lain.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 35,
    text: "Saya mudah marah ketika orang lain tidak setuju dengan saya.",
    ocean_type: 'A',
    isReversed: true
  },
  {
    id: 36,
    text: "Saya berusaha menghindari konflik dengan orang lain.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 37,
    text: "Saya cenderung kritis terhadap orang lain.",
    ocean_type: 'A',
    isReversed: true
  },
  {
    id: 38,
    text: "Saya senang bekerja sama dalam tim.",
    ocean_type: 'A',
    isReversed: false
  },
  {
    id: 39,
    text: "Saya lebih mementingkan kepentingan sendiri daripada orang lain.",
    ocean_type: 'A',
    isReversed: true
  },
  {
    id: 40,
    text: "Saya mudah memaafkan kesalahan orang lain.",
    ocean_type: 'A',
    isReversed: false
  },

  // Neuroticism (N) - 10 questions
  {
    id: 41,
    text: "Saya sering merasa cemas atau khawatir.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 42,
    text: "Saya mudah stres dalam situasi yang menantang.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 43,
    text: "Saya tetap tenang dalam situasi yang menekan.",
    ocean_type: 'N',
    isReversed: true
  },
  {
    id: 44,
    text: "Suasana hati saya sering berubah-ubah.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 45,
    text: "Saya jarang merasa sedih atau depresi.",
    ocean_type: 'N',
    isReversed: true
  },
  {
    id: 46,
    text: "Saya mudah tersinggung atau marah.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 47,
    text: "Saya memiliki kontrol emosi yang baik.",
    ocean_type: 'N',
    isReversed: true
  },
  {
    id: 48,
    text: "Saya sering merasa tidak aman atau ragu-ragu.",
    ocean_type: 'N',
    isReversed: false
  },
  {
    id: 49,
    text: "Saya mudah pulih dari kekecewaan atau kegagalan.",
    ocean_type: 'N',
    isReversed: true
  },
  {
    id: 50,
    text: "Saya sering merasa gelisah atau tidak bisa diam.",
    ocean_type: 'N',
    isReversed: false
  }
];

// OCEAN type descriptions
export const oceanDescriptions: OceanDescription[] = [
  {
    type: 'O',
    name: 'Openness to Experience',
    description: 'Keterbukaan terhadap pengalaman baru, kreativitas, dan keingintahuan intelektual. Orang dengan skor tinggi cenderung imajinatif, artistik, dan terbuka terhadap ide-ide baru.'
  },
  {
    type: 'C',
    name: 'Conscientiousness',
    description: 'Kecenderungan untuk terorganisir, bertanggung jawab, dan disiplin. Orang dengan skor tinggi cenderung dapat diandalkan, pekerja keras, dan berorientasi pada tujuan.'
  },
  {
    type: 'E',
    name: 'Extraversion',
    description: 'Kecenderungan untuk mencari stimulasi dari dunia luar dan berinteraksi dengan orang lain. Orang dengan skor tinggi cenderung sosial, energik, dan asertif.'
  },
  {
    type: 'A',
    name: 'Agreeableness',
    description: 'Kecenderungan untuk kooperatif, percaya, dan empati terhadap orang lain. Orang dengan skor tinggi cenderung ramah, altruistik, dan mudah bekerja sama.'
  },
  {
    type: 'N',
    name: 'Neuroticism',
    description: 'Kecenderungan untuk mengalami emosi negatif seperti kecemasan, depresi, dan ketidakstabilan emosi. Skor tinggi menunjukkan sensitivitas terhadap stres dan emosi negatif.'
  }
];

// Function to shuffle OCEAN questions randomly
export function getShuffledOceanQuestions(): OceanQuestion[] {
  const shuffled = [...oceanQuestions];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Function to calculate OCEAN scores from answers
export function calculateOceanScores(answers: Record<number, number>): OceanScores {
  const scores: OceanScores = { O: 0, C: 0, E: 0, A: 0, N: 0 };
  
  oceanQuestions.forEach(question => {
    const answer = answers[question.id];
    if (answer !== undefined) {
      // For reversed questions, flip the score (1->5, 2->4, 3->3, 4->2, 5->1)
      const adjustedScore = question.isReversed ? (6 - answer) : answer;
      scores[question.ocean_type] += adjustedScore;
    }
  });
  
  return scores;
}

// Likert scale options for OCEAN (same as RIASEC)
export const oceanLikertOptions = [
  { value: 1, label: 'Sangat Tidak Setuju' },
  { value: 2, label: 'Tidak Setuju' },
  { value: 3, label: 'Netral' },
  { value: 4, label: 'Setuju' },
  { value: 5, label: 'Sangat Setuju' }
];

import { OceanScores } from '@/lib/types';
import { oceanDescriptions } from '@/lib/oceanQuestions';

interface OceanChartProps {
  scores: OceanScores;
}

// Helper function to get trait level
function getTraitLevel(score: number): { level: string; color: string; percentage: number } {
  const percentage = (score / 50) * 100; // Convert to percentage (max score is 50)
  
  if (score <= 25) {
    return {
      level: 'Rendah',
      color: 'bg-red-500',
      percentage
    };
  } else if (score <= 35) {
    return {
      level: 'Sedang',
      color: 'bg-yellow-500',
      percentage
    };
  } else {
    return {
      level: 'Tinggi',
      color: 'bg-green-500',
      percentage
    };
  }
}

// Helper function to get trait name in Indonesian
function getTraitName(trait: string): string {
  const names = {
    'O': 'Keterbukaan',
    'C': 'Kehati-hatian',
    'E': 'Ekstraversi',
    'A': 'Keramahan',
    'N': 'Neurotisisme'
  };
  return names[trait as keyof typeof names] || trait;
}

// Helper function to get trait icon
function getTraitIcon(trait: string): string {
  const icons = {
    'O': '🎨',
    'C': '📋',
    'E': '🎉',
    'A': '🤝',
    'N': '😰'
  };
  return icons[trait as keyof typeof icons] || '📊';
}

export default function OceanChart({ scores }: OceanChartProps) {
  const traits = Object.entries(scores).map(([trait, score]) => {
    const traitInfo = getTraitLevel(score);
    const description = oceanDescriptions.find(d => d.type === trait);
    
    return {
      trait,
      name: getTraitName(trait),
      score,
      level: traitInfo.level,
      color: traitInfo.color,
      percentage: traitInfo.percentage,
      icon: getTraitIcon(trait),
      description: description?.description || ''
    };
  });

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8">
      <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
        Profil Kepribadian Big Five (OCEAN)
      </h2>
      
      <div className="space-y-6">
        {traits.map((trait) => (
          <div key={trait.trait} className="relative">
            {/* Trait Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <span className="text-2xl mr-3">{trait.icon}</span>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">
                    {trait.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {trait.description.substring(0, 80)}...
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-800">
                  {trait.score}
                </div>
                <div className={`text-sm font-medium px-2 py-1 rounded-full text-white ${
                  trait.level === 'Tinggi' ? 'bg-green-500' :
                  trait.level === 'Sedang' ? 'bg-yellow-500' : 'bg-red-500'
                }`}>
                  {trait.level}
                </div>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div 
                className={`h-4 rounded-full transition-all duration-1000 ease-out ${trait.color}`}
                style={{ width: `${trait.percentage}%` }}
              ></div>
            </div>

            {/* Score Range Indicator */}
            <div className="flex justify-between text-xs text-gray-500 mb-4">
              <span>10 (Rendah)</span>
              <span>30 (Sedang)</span>
              <span>50 (Tinggi)</span>
            </div>
          </div>
        ))}
      </div>

      {/* Legend */}
      <div className="mt-8 p-4 bg-gray-50 rounded-xl">
        <h4 className="text-sm font-semibold text-gray-700 mb-3">Interpretasi Skor:</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
            <span className="text-gray-600">Rendah (10-25): Skor di bawah rata-rata</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
            <span className="text-gray-600">Sedang (26-35): Skor rata-rata</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
            <span className="text-gray-600">Tinggi (36-50): Skor di atas rata-rata</span>
          </div>
        </div>
      </div>

      {/* Additional Info */}
      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
        <div className="flex items-start">
          <span className="text-blue-500 text-xl mr-3">💡</span>
          <div>
            <h4 className="text-sm font-semibold text-blue-800 mb-1">Tentang Big Five (OCEAN)</h4>
            <p className="text-sm text-blue-700">
              Model Big Five mengukur lima dimensi utama kepribadian yang stabil dan universal. 
              Setiap dimensi menggambarkan spektrum karakteristik kepribadian yang berbeda dan 
              membantu memahami pola perilaku, preferensi, dan gaya interaksi Anda.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
